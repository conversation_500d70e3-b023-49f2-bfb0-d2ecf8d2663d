{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 本筆記目的：\n", "\n", "1. 理解Linear Layer的輸入/輸出資料大小。\n", "2. 能簡單的利用Linear Layer來建立並訓練Multi-layer perceptron。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 測試Dense Layer I/O, 並以Dense Layer建立模型\n", "* Sequential model: Logistic Regression\n", "* Sequential model: Softmax Regression\n", "* 練習：建立Multilayer Perceptron模型，並且丟簡單資料進去做訓練"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "import pandas as pd\n", "import sklearn\n", "import os\n", "\n", "import torch\n", "\n", "sns.set()\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.nn import Sequential\n", "from torch.nn import Linear, Conv2d, MaxPool2d\n", "from torch.nn import Sigmoid, Softmax, ReLU\n", "\n", "from torch.optim import SGD\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "\n", "from torch.nn import CrossEntropyLoss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A. 測試Dense Layer I/O, 並以Dense Layer建立模型。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Logistic Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rand_data = np.random.normal(0, 1, (5, 3))\n", "rand_data = torch.Tensor(rand_data)  # 常態分佈的亂數資料當input;\n", "# 5個樣本，每個樣本有3個特徵"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定義模型\n", "model = Sequential(<PERSON><PERSON>(3, 1), <PERSON><PERSON><PERSON><PERSON>())\n", "\n", "model(rand_data)\n", "# 應該會回傳5個小於1的數值。分別為各樣本的預測機率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sequential model: Softmax Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rand_data = np.random.normal(0, 1, (5, 3))\n", "rand_data = torch.Tensor(rand_data)  # 常態分佈的亂數資料當input,\n", "# 5個樣本，每個樣本有3個特徵"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定義模型\n", "model = Sequential(Linear(3, 3), Softmax(dim=-1))\n", "\n", "model(rand_data).sum(axis=1)  # 驗證Softmax輸出：P_A+P_B+P_C=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 練習：建立Multilayer Perceptron的模型，並且將$X_{new}$, $y_{new}$丟進去做訓練。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = np.zeros((90, 3))\n", "for j in range(0, 30):\n", "    X[j, :] = 0.0\n", "for j in range(30, 60):\n", "    X[j, :] = 1.0\n", "for j in range(60, 90):\n", "    X[j, :] = 2.0\n", "\n", "y = X[:, 0].astype(np.int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data standarization\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "scaler = StandardScaler()\n", "scaler = scaler.fit(X)\n", "X_new = scaler.transform(X)\n", "\n", "# One-hot encoding\n", "y_new = np.eye(3)[y]\n", "\n", "X_new = X_new.astype(np.float32)\n", "y_new = y_new.astype(np.float32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# #=== 建立模型 ===\n", "#\n", "# 練習於此\n", "#\n", "# model = Sequential(...)\n", "#"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_step(\n", "    dataloader,\n", "    model,\n", "    loss_fn,\n", "    optimizer,\n", "    verbose_every=999,\n", "):\n", "    \"\"\"訓練一個epoch。\"\"\"\n", "    for iteration, (batch_x, batch_y) in enumerate(dataloader):\n", "\n", "        optimizer.zero_grad()\n", "\n", "        #########################\n", "        ## 練習: 添加正傳遞與倒傳遞 #\n", "        #\n", "        # pred_y = ...\n", "        # ...\n", "        # xxx.backward()\n", "        #\n", "        #########################\n", "\n", "        if iteration + 1 % verbose_every == 0:\n", "            loss = loss_value.item()\n", "            print(\"loss={:.4f}\".format(iteration, loss))\n", "\n", "        optimizer.step()  # 梯度更新只要執行 step()即可。這個步驟會將每個權重\n", "\n", "\n", "def test_step(\n", "    dataloader,\n", "    model,\n", "    loss_fn,\n", "):\n", "    \"\"\"結束一個epoch的訓練後，測試模型表現。\"\"\"\n", "    size = len(dataloader.dataset)\n", "    test_loss, correct = 0, 0\n", "\n", "    with torch.no_grad():\n", "        for iteration, (batch_x, batch_y) in enumerate(dataloader):\n", "            pred_y = model(batch_x)\n", "\n", "            test_loss += loss_fn(pred_y, batch_y).item()\n", "            correct += (pred_y.argmax(axis=1) == batch_y).type(torch.float).sum().item()\n", "\n", "    test_loss /= size\n", "    correct /= size\n", "\n", "    print(\"test_loss={:.4f}, accuracy={:.2f}\".format(test_loss, correct))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 載入資料成為DataLoader\n", "\n", "X_new, y_new = torch.tensor(X_new), torch.tensor(y_new)\n", "\n", "y = torch.tensor(y).to(torch.int64)\n", "td = TensorDataset(X_new, y)\n", "dl = DataLoader(td, batch_size=10, shuffle=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 宣告模型訓練設定\n", "num_epochs = 20\n", "learning_rate = 0.1\n", "\n", "# 定義優化器, Loss函數\n", "ce_loss = CrossEntropyLoss()\n", "opt = SGD(model.parameters(), lr=learning_rate)\n", "\n", "# 訓練模型\n", "for j in range(num_epochs):\n", "    train_step(dl, model, ce_loss, opt)\n", "    test_step(dl, model, ce_loss)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.6"}}, "nbformat": 4, "nbformat_minor": 4}