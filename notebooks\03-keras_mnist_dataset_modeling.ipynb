{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 本筆記將帶大家用Keras建模\n", "# 此範例使用MNIST手寫數字資料集"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "# 索引\n", "\n", "[1. 資料準備](#I.-資料準備)\n", "  * [1.a. 載入圖片](#1.a.-載入圖片)\n", "  * [1.b. 利用Pandas可迅速了解每個資料夾裡面有幾張圖片](#1.b.-利用Pandas可迅速了解每個資料夾裡面有幾張圖片)\n", "  * [1.c. 將圖片路徑資料切割成三份：取其80%做為train-data。 之後，剩下的20%中的10%做validation data，10%做test data](#1.c.-將圖片路徑資料切割成三份：取其80%做為train-data。-之後，剩下的20%中的10%做validation-data，10%做test-data)\n", "  * [1.d. 將圖片載入，存成數值矩陣](#1.d.-將圖片載入，存成數值矩陣)\n", "  \n", "\n", "[2. Softmax Regression](#2.-Softmax-Regression)\n", "\n", "  * [2.a. 將y 轉成one hot形式](#2.a.-將y-轉成one-hot形式)\n", "  * [2.b. 開始建立模型](#2.b.-開始建立模型)\n", "  * [2.c. 開始訓練模型](#2.c.-開始訓練模型)\n", "  * [2.d. 檢視訓練好的模型用於test data有多少準確率](#2.d.-檢視訓練好的模型用於test-data有多少準確率)\n", "  * [2.e. 檢視50筆測試資料的預測結果，以稍為了解預測是否還算ok](#2.e.-檢視50筆測試資料的預測結果，以稍為了解預測是否還算ok)\n", "  * [2.f. 畫出模型訓練過程](#2.f.-畫出模型訓練過程)\n", "  * [2.g. 儲存模型和權重](#2.g.-儲存模型和權重)\n", "  * [2.h. 載入存好的模型和權重](#2.h.-載入存好的模型和權重)\n", "  * [2.i. 輸出分類報告](#2.i.-輸出分類報告)\n", "  \n", "[3. Simple Convolutional Neural Network](#3.-Simple-Convolutional-Neural-Network)\n", "\n", "  * [3.a. 建立模型](#3.a.-建立模型)\n", "  * [3.b. 訓練模型](#3.b.-訓練模型)\n", "  * [3.c. 檢視模型訓練結果](#3.c.-檢視模型訓練結果)\n", "  * [3.d. 檢視模型準確率](#3.d.-檢視模型準確率)\n", "  * [3.e. 檢視分類報告](#3.e.-檢視分類報告)\n", "  \n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "os.environ[\"KERAS_BACKEND\"] = \"torch\"\n", "\n", "import keras\n", "\n", "keras.config.backend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "sns.set()\n", "import pandas as pd\n", "import sklearn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 資料準備"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.a. 載入圖片"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def filePathsGen(rootPath):\n", "    \"\"\"此函數將rootPath資料夾目錄中的所有圖片路徑資訊儲存至一個清單內。\"\"\"\n", "    paths = []\n", "    dirs = []\n", "    for dirPath, dirNames, fileNames in os.walk(rootPath):\n", "        for fileName in fileNames:\n", "            fullPath = os.path.join(dirPath, fileName)\n", "            paths.append((int(dirPath[len(rootPath)]), fullPath))\n", "        dirs.append(dirNames)\n", "    return dirs, paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls ../datasets/mnist"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dirs, paths = filePathsGen(\"../datasets/mnist/\")  # 載入圖片路徑\n", "\n", "dfPath = pd.DataFrame(paths, columns=[\"class\", \"path\"])  # 圖片路徑存成Pandas資料表\n", "dfPath.head(3)  # 看資料表前3個row"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.b. 利用Pandas可迅速了解每個資料夾裡面有幾張圖片"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 依照class分群後，數各群的數量，並繪圖\n", "dfCountPerClass = dfPath.groupby(\"class\").count()\n", "dfCountPerClass.rename(columns={\"path\": \"amount of figures\"}, inplace=True)\n", "dfCountPerClass.plot(kind=\"bar\", rot=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["####  1.c. 將圖片路徑資料切割成三份：取其80%做為train data。 之後，剩下的20%中取一半做validation data，另一半做test data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfFrac = dfPath.sample(frac=1)  # 打亂一下path data\n", "\n", "train = dfFrac.sample(frac=0.8)  # 將path data隨機取樣，80%的path data當train\n", "test = dfFrac.drop(train.index)  # 20%的path data當test\n", "\n", "trainVal = test.sample(frac=0.5)\n", "test = test.drop(trainVal.index)\n", "\n", "print(\"shape(all figures)=\\t\\t\", dfPath.shape)\n", "print(\"shape(fraction of figures)=\\t\", dfFrac.shape)\n", "print(\"shape(train)=\\t\\t\\t\", train.shape)\n", "print(\"shape(trainVal)=\\t\\t\", trainVal.shape)\n", "print(\"shape(test)=\\t\\t\\t\", test.shape)\n", "\n", "# 隨便抓三張圖來看\n", "for j in range(3):\n", "    img = plt.imread(train[\"path\"].iloc[j])\n", "    plt.imshow(img, cmap=\"gray\")\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["####  1.d. 將圖片載入，存成數值矩陣"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dataLoad(dfPath):\n", "    paths = dfPath[\"path\"].values\n", "    x = np.zeros((len(paths), 28, 28), dtype=np.float32)\n", "    for j in range(len(paths)):\n", "        x[j, :, :] = plt.imread(paths[j]) / 255.0\n", "\n", "    y = dfPath[\"class\"].values\n", "    return x, y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainX, trainY = dataLoad(train)\n", "trainValX, trainValY = dataLoad(trainVal)\n", "testX, testY = dataLoad(test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"train:\\t\", trainX.shape, trainY.shape)\n", "print(\"trainVal:\", trainValX.shape, trainValY.shape)\n", "print(\"test:\\t\", testX.shape, testY.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Softmax Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Sequential\n", "from keras.layers import Dense, Flatten\n", "from keras.optimizers import SGD"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.a. 將y 轉成one hot形式"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "\n", "enc = OneHotEncoder()\n", "trainYOneHot = enc.fit_transform(trainY.reshape(-1, 1)).toarray()\n", "\n", "trainValYOneHot = enc.fit_transform(trainValY.reshape(-1, 1)).toarray()\n", "\n", "testYOneHot = enc.fit_transform(testY.reshape(-1, 1)).toarray()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"train:\\t\", trainX.shape, trainY.shape)\n", "print(\"trainVal:\", trainValX.shape, trainValY.shape)\n", "print(\"test:\\t\", testX.shape, testY.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.b. 開始建立模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.layers import Flatten"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = Sequential()\n", "model.add(Flatten(input_shape=(28, 28)))  # 此層將28X28的資料攤成1維\n", "model.add(Den<PERSON>(10, activation=\"softmax\"))  # 此層將以十個神經元輸出十種數字的個別機率\n", "\n", "sgd = SGD(lr=0.05)\n", "model.compile(\n", "    optimizer=sgd, loss=\"categorical_crossentropy\", metrics=[\"accuracy\"]\n", ")  # 告知模型訓練方式"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 檢視一下所訓練的模型\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.c. 開始訓練模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hist = model.fit(\n", "    trainX,\n", "    trainYOneHot,\n", "    epochs=20,\n", "    batch_size=128,\n", "    validation_data=(trainValX, trainValYOneHot),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.d. 檢視訓練好的模型用於test data有多少準確率"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = model.evaluate(testX, testYOneHot, batch_size=128)\n", "print()\n", "print(\"\\nloss=%s \\naccuracy=%s\" % (score[0], score[1]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.e. 檢視50筆測試資料的預測結果，以稍為了解預測是否還算ok"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for j in range(50):\n", "    predY = model.predict(trainValX[j : j + 1, :]).argmax()\n", "    trueY = trainValYOneHot[j].argmax()\n", "    print(predY, trueY, end=\"\\t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.f. 畫出模型訓練過程"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(hist.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(hist.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.g. 儲存模型和權重"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"first_try.json\", \"w\") as jsOut:\n", "    json.dump(model.to_json(), jsOut)\n", "\n", "model.save_weights(\"first_try.h5\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.h. 載入存好的模型和權重"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import model_from_json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"first_try.json\", \"r\") as jsIn:\n", "    modelJson = json.load(jsIn)\n", "\n", "modelLoaded = model_from_json(modelJson)\n", "modelLoaded.load_weights(\"first_try.h5\")\n", "\n", "modelLoaded.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.i. 輸出分類報告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import classification_report\n", "\n", "predY = model.predict(testX).argmax(axis=1)\n", "print(classification_report(testY, predY))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simple Convolutional Neural Network"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainX = trainX.reshape(*trainX.shape, 1)\n", "trainValX = trainValX.reshape(*trainValX.shape, 1)\n", "testX = testX.reshape(*testX.shape, 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.a. 建立模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from keras.models import Sequential\n", "from keras.layers import Dense, Dropout, Flatten, Conv2D, MaxPooling2D\n", "from keras.layers import Activation\n", "from keras.optimizers import SGD, Adam, Adamax\n", "\n", "input_shape = (28, 28, 1)\n", "\n", "model = Sequential()\n", "\n", "# conv1\n", "model.add(\n", "    Conv2D(filters=32, kernel_size=(3, 3), activation=\"relu\", input_shape=input_shape)\n", ")\n", "\n", "# conv2\n", "model.add(Conv2D(filters=64, kernel_size=(3, 3), activation=\"relu\"))\n", "# pool1\n", "model.add(MaxPooling2D(pool_size=(2, 2)))\n", "# conv3\n", "model.add(Conv2D(filters=64, kernel_size=(3, 3), activation=\"relu\"))\n", "# pool2\n", "model.add(MaxPooling2D(pool_size=(2, 2)))\n", "# dropout1\n", "model.add(Dropout(0.5))\n", "model.add(<PERSON><PERSON>())\n", "# dense1\n", "model.add(<PERSON><PERSON>(128, activation=\"relu\"))\n", "# dropout2\n", "model.add(Dropout(0.5))\n", "# dense2\n", "model.add(<PERSON><PERSON>(10, activation=\"softmax\"))\n", "\n", "model.compile(\n", "    loss=\"categorical_crossentropy\",\n", "    optimizer=SGD(lr=0.05),\n", "    metrics=[\"accuracy\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.b. 訓練模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time \n", "hist = model.fit(trainX, trainYOneHot, \n", "                 epochs=30,\n", "                 batch_size=128,\n", "                 validation_data=(trainValX,trainValYOneHot),)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.c. 檢視模型訓練結果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(hist.history[\"accuracy\"], ms=5, marker=\"o\", label=\"accuracy\")\n", "plt.plot(hist.history[\"val_accuracy\"], ms=5, marker=\"o\", label=\"val accuracy\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.d. 檢視模型準確率"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = model.evaluate(testX, testYOneHot, batch_size=128)\n", "print()\n", "print(\"\\nloss=%s \\naccuracy=%s\" % (score[0], score[1]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.e. 檢視分類報告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import classification_report\n", "\n", "predY = model.predict(testX).argmax(axis=1)\n", "print(classification_report(testY, predY))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[回索引](#索引)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習\n", "\n", "調整參數。嘗試增加或減少模型複雜度，並再次訓練，看模型準確度能否有所提升。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "torchv2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}