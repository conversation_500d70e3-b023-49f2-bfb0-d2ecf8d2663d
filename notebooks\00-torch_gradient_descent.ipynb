{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["目的: 初步理解如何使用PyTorch進行倒數計算和模型優化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習1: $y = 3x$，求$\\frac{dy}{dx}|_{x=10}$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習2: $y = 3x^2$，求$\\frac{dy}{dx}|_{x=10}$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習3: $z = (x+y)^2$，求$\\frac{\\partial z}{\\partial y}|_{(x=10,y=5)}$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習4: 試用PyTorch計算$\\frac{d\\sin(x)}{dx}$。並將$sin(x)$以及$\\frac{d\\sin(x)}{dx}$透過Matplotlib畫圖。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 練習5: $Loss=|w_1| + |w_2|$。請用Gradient Descent優化器，優化出可將此$Loss$最小化的最適$w_1$和$w_2$。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "torchv2", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}